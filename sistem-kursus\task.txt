Fitur yang Akan Dibuat
Berikut adalah daftar fitur utama yang direncanakan untuk pengembangan aplikasi:
● Modul Otentikasi & Otorisasi:
o Registrasi dan login pengguna.
o Manaj<PERSON>en peran (<PERSON><PERSON>, Teacher, Student)
o Pembatasan akses berdasarkan peran.
o Reset kata sandi.
● Modul Manajemen Pendaftaran Kursus:
o 1.1 Murid dapat mendaftar kursus untuk pertama kali menggunakan akun dan
nomor telepon
o 1.2 Admin dapat melihat dan memverifikasi data pendaftaran
o 1.3 Admin dapat mengubah status pendaftaran (Pending, <PERSON><PERSON>ima, <PERSON><PERSON>lak)
● Modul Manajemen Jadwal Kursus:
o 2.1 Entity murid dapat melihat jadwal kursus
o 2.2 Entity murid dapat mencari jadwal kursus dengan filter dan search bar
o 2.3 Murid dapat mengunduh file jadwal kursus
● Modul Manajemen Ulasan Kursus:
o 3.1 Murid dapat memberikan ulasan/rating beserta dengan review terhadap
kursus
o 3.2 Sistem menyimpan ulasan dan menampilkannya pada halaman detail
kursus
● Modul Manajemen Pembayaran Kursus:
○ 4.1 Siswa dapat mengunggah bukti pembayaran kursus
○ 4.2 Admin dapat memverifikasi bukti pembayaran dan mengubah status
pembayaran
○ 4.3 Riwayat pembayaran tersimpan dan dapat diakses oleh pengguna
● Laporan dan Analitik:
o Laporan jumlah siswa terdaftar per kursus.
o Statistik pendapatan bulanan dan tahunan
o Laporan rating kursus dan pengajar
● Pengaturan Umum:
o Manajemen profil pengguna.
o Pengaturan sistem dasar oleh admin.
● Modul Notifikasi
o Pengingat jadwal, notifikasi pembayaran.
● Modul forum diskusi
o Diskusi antara murid dan pengajar dalam satu kursus
Desain Database (ERD - Entity Relationship Diagram)
Berikut adalah representasi kasar Desain Database (ERD) untuk aplikasi ini. Mohon dicatat
bahwa ini adalah gambaran awal dan dapat berkembang selama proses pengembangan.
(Anda bisa menggambar ERD secara manual atau menggunakan tool seperti draw.io,
kemudian menyisipkannya ke dalam dokumen ini. Jika Anda memilih untuk mendeskripsikan
tabel dan relasinya dalam teks, contoh di bawah ini dapat membantu)
Contoh Deskripsi ERD:
Tabel users → menyimpan data pengguna (siswa/pengajar)
● id (INT, Primary Key, Auto Increment)
● name (VARCHAR(255))
● email (VARCHAR(255), Unique)
● password (VARCHAR(255))
● role_id (INT, Foreign Key ke roles.id)
● created_at (TIMESTAMP)
● updated_at (TIMESTAMP)
Tabel roles
● id (INT, Primary Key, Auto Increment)
● name (VARCHAR(50), Unique)
● description (TEXT, Nullable)
Tabel course → menyimpan informasi kursus
● id (INT, Primary Key, Auto Increment)
● title (VARCHAR(255))
● description (TEXT)
● instructor_id (INT, Foreign Key ke users.id)
● price (DECIMAL(10,2))
● created_at (TIMESTAMP)
● updated_at (TIMESTAMP)
Tabel enrollments → mencatat pendaftaran siswa ke kursus
● id (INT, Primary Key, Auto Increment)
● user_id (INT, Foreign Key ke users.id)
● course_id (INT, Foreign Key ke courses.id)
● enrolled_at (TIMESTAMP)
● status (ENUM: 'active', 'completed', 'cancelled')
Tabel materials → menyimpan materi pembelajaran kursus
● id (INT, Primary Key, Auto Increment)
● course_id (INT, Foreign Key ke courses.id)
● title (VARCHAR(255))
● content (TEXT) — bisa berupa link video, PDF, atau teks
● order (INT) — urutan materi dalam kursus
● created_at (TIMESTAMP)
Relasi
● users → roles
many-to -one
setiap user memiliki 1 role, tetapi 1 roles dimiliki banyak user
● course → user(pengajar)
many-to-one
setiap kursus dibuat oleh 1 pengajar, tetapi 1 pengajar bisa membuat banyak kursus
● enrollments → user(siswa)
many to many
1 siswa bisa mengikuti banyak kursus, 1 kursus bisa diikuti banyak siswa
● course → materials
one to many
1 kursus bisa memiliki banyak materi
Desain Kasaran UI (Mockup)
Berikut merupakan penjelasan singkat mengenai rancangan tampilan tiap halaman yang
disesuaikan dengan peran pengguna dan fungsinya dalam sistem.
Contoh Deskripsi Mockup:
Halaman Login :
1. Pintu masuk utama bagi pengguna, elemen yang ada disesuaikan dengan tabel users.
Komponen:
- Input Email (users.email)
- Input Password (users.password)
- Tombol “Masuk”
- “Lupa Kata Sandi?”
- “Daftar Akun Baru”
Kebutuhan Data:
- users.email dan users. password
Dashboard Pengguna :
2. A. Siswa
- Sidebar menu:
- Dashboard
- Kursus Saya
- Keluar
Konten utama:
- Daftar kursus yang diikuti
- Status tiap kursus: Active / Completed / Cancelled
- Tombol Lihat Detail
B. Pengajar
- Sidebar menu:
- Dashboard
- Kursus Saya
- Materi
- Keluar
Konten utama:
- Daftar kursus yang diajarkan
- Jumlah siswa terdaftar
- Tombol Kelola Materi
Manajemen Pengguna:
3. Tabel daftar pengguna:
- Kolom: Nama, Email, Role, Tanggal Daftar
- Tombol aksi: Edit, Hapus
Tombol Tambah Pengguna membuka form:
- Input Nama
- Input Email
- Input Password
- Dropdown Role
Halaman Kursus:
4. Siswa :
Daftar kursus :
- Judul
- Deskripsi singkat
- Nama pengajar
- Harga
- Tombol Daftar
Pengajar / Admin :
Tabel kursus:
- Judul
- Harga
- Tanggal dibuat
- Tombol aksi: Lihat, Edit, Hapus
Tombol Tambah Kursus membuka form:
- Input Judul
- Input Deskripsi
- Input Harga
Detail Kursus (Siswa):
5. Judul halaman: Detail Kursus
Konten:
- Judul kursus
- Nama pengajar
- Deskripsi
- Daftar materi urut sesuai topik
- Tombol Lihat Materi
Halaman Pendaftaran (Admin) :
6. Tabel pendaftaran kursus:
- Kolom: Nama siswa, Judul kursus, Status, Tanggal daftar
- Filter berdasarkan status
Tombol Tambah Pendaftaran Manual membuka form:
- Dropdown Pilih Siswa
- Dropdown Pilih Kursus
- Dropdown Status
Halaman Materi (Pengajar) :
7. Dropdown pilih kursus
Tabel materi:
- Judul materi
- Urutan
- Link atau ringkasan konten
- Tombol: Edit, Hapus
Tombol Tambah Materi membuka form:
- Input Judul
- Input Konten
- Input Urutan
Halaman Pembayaran + Pendaftaran :
8. Ringkasan kursus:
- Judul
- Nama pengajar
- Harga
Form pembayaran:
- Pilih metode pembayaran
- Upload bukti pembayaran
- Tombol Bayar dan Daftar
Setelah sukses, pengguna langsung diarahkan ke halaman kursus