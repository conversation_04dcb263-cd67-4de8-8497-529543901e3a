@extends('layouts.app')

@section('title', 'Riwayat Pembayaran - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-credit-card me-2 text-primary"></i>Riwayat Pembayaran
                    </h1>
                    <p class="text-muted mb-0">Pantau status pembayaran kursus Anda</p>
                </div>
                <div>
                    <a href="{{ route('courses.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Cari Kursus
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h5>{{ $payments->where('status', 'pending')->count() }}</h5>
                    <small class="text-muted">Menunggu Verifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5>{{ $payments->where('status', 'verified')->count() }}</h5>
                    <small class="text-muted">Terverifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                    <h5>{{ $payments->where('status', 'rejected')->count() }}</h5>
                    <small class="text-muted">Ditolak</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-money-bill fa-2x text-info mb-2"></i>
                    <h5>Rp {{ number_format($payments->where('status', 'verified')->sum('amount'), 0, ',', '.') }}</h5>
                    <small class="text-muted">Total Dibayar</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments List -->
    <div class="row">
        @forelse($payments as $payment)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ $payment->course->title }}</h6>
                        <span class="badge bg-{{ $payment->status == 'verified' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                            @if($payment->status == 'verified')
                                <i class="fas fa-check me-1"></i>Terverifikasi
                            @elseif($payment->status == 'pending')
                                <i class="fas fa-clock me-1"></i>Pending
                            @else
                                <i class="fas fa-times me-1"></i>Ditolak
                            @endif
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h5 class="text-primary mb-1">{{ $payment->formatted_amount ?? 'Rp ' . number_format($payment->amount, 0, ',', '.') }}</h5>
                            <small class="text-muted">
                                <i class="fas fa-credit-card me-1"></i>{{ $payment->payment_method }}
                            </small>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Dibayar: {{ $payment->created_at->format('d M Y, H:i') }}
                            </small>
                        </div>

                        @if($payment->verified_at)
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Diverifikasi: {{ $payment->verified_at->format('d M Y, H:i') }}
                                </small>
                            </div>
                        @endif

                        @if($payment->notes)
                            <div class="mb-3">
                                <div class="alert alert-{{ $payment->status == 'verified' ? 'success' : 'danger' }} alert-sm">
                                    <small>
                                        <i class="fas fa-sticky-note me-1"></i>
                                        <strong>Catatan Admin:</strong> {{ $payment->notes }}
                                    </small>
                                </div>
                            </div>
                        @endif

                        <!-- Status Timeline -->
                        <div class="mb-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success rounded-circle p-1" style="width: 20px; height: 20px;">
                                        <i class="fas fa-check text-white" style="font-size: 10px;"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <div class="bg-{{ $payment->status != 'pending' ? 'success' : 'secondary' }}" style="height: 2px;"></div>
                                </div>
                                <div class="flex-shrink-0">
                                    <div class="bg-{{ $payment->status == 'verified' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }} rounded-circle p-1" style="width: 20px; height: 20px;">
                                        <i class="fas fa-{{ $payment->status == 'verified' ? 'check' : ($payment->status == 'pending' ? 'clock' : 'times') }} text-white" style="font-size: 10px;"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">Upload</small>
                                <small class="text-muted">
                                    @if($payment->status == 'verified')
                                        Selesai
                                    @elseif($payment->status == 'pending')
                                        Verifikasi
                                    @else
                                        Ditolak
                                    @endif
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            @if($payment->proof_file)
                                <a href="{{ route('student.payments.download', $payment) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-download me-1"></i>Unduh Bukti
                                </a>
                            @endif
                            
                            <a href="{{ route('courses.show', $payment->course) }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-eye me-1"></i>Lihat Kursus
                            </a>

                            @if($payment->status == 'verified')
                                <div class="text-center">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Pembayaran berhasil diverifikasi
                                    </small>
                                </div>
                            @elseif($payment->status == 'pending')
                                <div class="text-center">
                                    <small class="text-warning">
                                        <i class="fas fa-clock me-1"></i>
                                        Menunggu verifikasi admin
                                    </small>
                                </div>
                            @else
                                <div class="text-center">
                                    <small class="text-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Pembayaran ditolak
                                    </small>
                                </div>
                                @if($payment->notes)
                                    <a href="{{ route('student.payments.create', $payment->course) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-redo me-1"></i>Upload Ulang
                                    </a>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Belum Ada Pembayaran</h4>
                    <p class="text-muted">Anda belum melakukan pembayaran untuk kursus apapun.</p>
                    <a href="{{ route('courses.index') }}" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Jelajahi Kursus
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($payments->hasPages())
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $payments->links() }}
                </div>
            </div>
        </div>
    @endif

    <!-- Payment Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Bantuan Pembayaran
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Status Pembayaran</h6>
                            <ul class="list-unstyled">
                                <li class="mb-1">
                                    <span class="badge bg-warning me-2">Pending</span>
                                    Menunggu verifikasi admin (1-24 jam)
                                </li>
                                <li class="mb-1">
                                    <span class="badge bg-success me-2">Verified</span>
                                    Pembayaran berhasil diverifikasi
                                </li>
                                <li class="mb-1">
                                    <span class="badge bg-danger me-2">Rejected</span>
                                    Pembayaran ditolak, silakan upload ulang
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Tips Pembayaran</h6>
                            <ul class="list-unstyled">
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Pastikan bukti pembayaran jelas dan lengkap
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Upload file dengan format JPG, PNG, atau PDF
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Jumlah pembayaran harus sesuai dengan harga kursus
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
