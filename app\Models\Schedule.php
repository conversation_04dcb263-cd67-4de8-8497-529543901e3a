<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Schedule extends Model
{
    protected $fillable = [
        'course_id',
        'title',
        'description',
        'date',
        'start_time',
        'end_time',
        'location',
        'type',
        'meeting_link',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
    ];

    /**
     * Get the course that owns the schedule.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }
}
