@extends('layouts.app')

@section('title', 'Dashboard Siswa - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-2">
                                <i class="fas fa-user-graduate me-2"></i>
                                Selamat datang, {{ auth()->user()->name }}!
                            </h2>
                            <p class="card-text mb-0">
                                Ke<PERSON>la pembelajaran Anda dan pantau progress kursus yang sedang diikuti.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-graduation-cap fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover">
                <div class="card-body">
                    <i class="fas fa-book-open fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">Kursus Aktif</h5>
                    <h3 class="text-primary">{{ auth()->user()->enrollments()->where('status', 'active')->count() }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Kursus Selesai</h5>
                    <h3 class="text-success">{{ auth()->user()->enrollments()->where('status', 'completed')->count() }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover">
                <div class="card-body">
                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">Review Diberikan</h5>
                    <h3 class="text-warning">{{ auth()->user()->reviews()->count() }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover">
                <div class="card-body">
                    <i class="fas fa-credit-card fa-2x text-info mb-2"></i>
                    <h5 class="card-title">Pembayaran</h5>
                    <h3 class="text-info">{{ auth()->user()->payments()->where('status', 'verified')->count() }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- My Courses -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Kursus Saya
                    </h5>
                    <a href="{{ route('courses.index') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Cari Kursus
                    </a>
                </div>
                <div class="card-body">
                    @forelse(auth()->user()->enrollments()->with('course')->latest()->take(5)->get() as $enrollment)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $enrollment->course->title }}</h6>
                                <small class="text-muted">
                                    Bergabung: {{ $enrollment->enrolled_at->format('d M Y') }}
                                </small>
                                <br>
                                <span class="badge bg-{{ $enrollment->status == 'active' ? 'success' : ($enrollment->status == 'completed' ? 'primary' : 'secondary') }}">
                                    {{ ucfirst($enrollment->status) }}
                                </span>
                            </div>
                            <div>
                                <a href="{{ route('courses.show', $enrollment->course) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>Lihat
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Anda belum mengikuti kursus apapun.</p>
                            <a href="{{ route('courses.index') }}" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Jelajahi Kursus
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Aksi Cepat
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('courses.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Cari Kursus
                        </a>
                        <a href="{{ route('student.enrollments.index') }}" class="btn btn-outline-success">
                            <i class="fas fa-list me-2"></i>Kursus Saya
                        </a>
                        <a href="{{ route('student.payments.index') }}" class="btn btn-outline-info">
                            <i class="fas fa-credit-card me-2"></i>Riwayat Pembayaran
                        </a>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-user-cog me-2"></i>Pengaturan Profil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Notifikasi Terbaru
                    </h6>
                </div>
                <div class="card-body">
                    @forelse(auth()->user()->notifications()->latest()->take(3)->get() as $notification)
                        <div class="d-flex align-items-start mb-2">
                            <i class="fas fa-circle text-primary me-2 mt-1" style="font-size: 0.5rem;"></i>
                            <div class="flex-grow-1">
                                <small class="fw-semibold">{{ $notification->title }}</small>
                                <br>
                                <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                            </div>
                        </div>
                    @empty
                        <small class="text-muted">Tidak ada notifikasi baru</small>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
