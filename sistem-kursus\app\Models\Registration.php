<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Registration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'course_id',
        'registration_date',
        'status',
        'payment_status',
        'payment_amount',
        'payment_method',
        'notes'
    ];

    protected $casts = [
        'registration_date' => 'datetime',
        'payment_amount' => 'decimal:2',
    ];

    protected $dates = [
        'registration_date',
        'deleted_at'
    ];

    /**
     * Get the user that owns the registration
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that owns the registration
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Scope for active registrations
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for paid registrations
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Check if registration is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if payment is completed
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Get status badge class for display
     */
    public function getStatusBadgeClass()
    {
        switch ($this->status) {
            case 'active':
                return 'badge-success';
            case 'inactive':
                return 'badge-secondary';
            case 'suspended':
                return 'badge-warning';
            case 'completed':
                return 'badge-info';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get payment status badge class for display
     */
    public function getPaymentStatusBadgeClass()
    {
        switch ($this->payment_status) {
            case 'paid':
                return 'badge-success';
            case 'pending':
                return 'badge-warning';
            case 'failed':
                return 'badge-danger';
            case 'refunded':
                return 'badge-info';
            default:
                return 'badge-secondary';
        }
    }
}