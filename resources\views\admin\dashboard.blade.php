@extends('layouts.app')

@section('title', 'Dashboard Admin - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-2">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard Administrator
                            </h2>
                            <p class="card-text mb-0">
                                Selamat datang, {{ auth()->user()->name }}! Kelola sistem kursus dengan mudah.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-cogs fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-book fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">Total Kursus</h5>
                    <h3 class="text-primary">{{ \App\Models\Course::count() }}</h3>
                    <small class="text-muted">Kursus tersedia</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Total Siswa</h5>
                    <h3 class="text-success">{{ \App\Models\User::whereHas('role', function($q) { $q->where('name', 'Student'); })->count() }}</h3>
                    <small class="text-muted">Siswa terdaftar</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-chalkboard-teacher fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">Total Pengajar</h5>
                    <h3 class="text-warning">{{ \App\Models\User::whereHas('role', function($q) { $q->where('name', 'Teacher'); })->count() }}</h3>
                    <small class="text-muted">Pengajar aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-user-graduate fa-2x text-info mb-2"></i>
                    <h5 class="card-title">Total Pendaftaran</h5>
                    <h3 class="text-info">{{ \App\Models\Enrollment::count() }}</h3>
                    <small class="text-muted">Pendaftaran kursus</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Courses -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Kursus Terbaru
                    </h5>
                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Tambah Kursus
                    </a>
                </div>
                <div class="card-body">
                    @forelse(\App\Models\Course::with('instructor')->latest()->take(5)->get() as $course)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $course->title }}</h6>
                                <small class="text-muted">
                                    Pengajar: {{ $course->instructor->name ?? 'Belum ditentukan' }}
                                </small>
                                <br>
                                <small class="text-muted">
                                    Dibuat: {{ $course->created_at->format('d M Y') }}
                                </small>
                            </div>
                            <div>
                                <span class="badge bg-primary">{{ $course->formatted_price }}</span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-book fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Belum ada kursus</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Recent Enrollments -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-graduate me-2"></i>Pendaftaran Terbaru
                    </h5>
                    <a href="{{ route('admin.enrollments.index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-1"></i>Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @forelse(\App\Models\Enrollment::with(['user', 'course'])->latest()->take(5)->get() as $enrollment)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $enrollment->user->name }}</h6>
                                <small class="text-muted">{{ $enrollment->course->title }}</small>
                                <br>
                                <small class="text-muted">{{ $enrollment->enrolled_at->format('d M Y') }}</small>
                            </div>
                            <div>
                                <span class="badge bg-{{ $enrollment->status == 'active' ? 'success' : ($enrollment->status == 'completed' ? 'primary' : 'secondary') }}">
                                    {{ ucfirst($enrollment->status) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-user-graduate fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Belum ada pendaftaran</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Aksi Cepat
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ route('admin.courses.create') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>Tambah Kursus
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ route('admin.courses.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>Kelola Kursus
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ route('admin.enrollments.index') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-user-graduate me-2"></i>Kelola Pendaftaran
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-credit-card me-2"></i>Kelola Pembayaran
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
