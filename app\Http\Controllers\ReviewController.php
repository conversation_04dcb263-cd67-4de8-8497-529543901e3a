<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Review;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    /**
     * Display reviews for a course.
     */
    public function index(Course $course)
    {
        $reviews = $course->reviews()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('courses.reviews.index', compact('course', 'reviews'));
    }

    /**
     * Store a new review.
     */
    public function store(Request $request, Course $course)
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
        ]);

        $user = Auth::user();

        // Check if user is enrolled in the course
        if (!$user->enrollments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda harus terdaftar di kursus ini untuk memberikan review.');
        }

        // Check if user already reviewed this course
        if ($user->reviews()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda sudah memberikan review untuk kursus ini.');
        }

        // Create review
        Review::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'rating' => $request->rating,
            'review' => $request->review,
        ]);

        return redirect()->back()->with('success', 'Review berhasil ditambahkan. Terima kasih atas feedback Anda!');
    }

    /**
     * Update an existing review.
     */
    public function update(Request $request, Review $review)
    {
        // Check if user owns this review
        if (Auth::id() !== $review->user_id) {
            return redirect()->back()->with('error', 'Anda tidak memiliki akses untuk mengubah review ini.');
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
        ]);

        $review->update([
            'rating' => $request->rating,
            'review' => $request->review,
        ]);

        return redirect()->back()->with('success', 'Review berhasil diperbarui.');
    }

    /**
     * Delete a review.
     */
    public function destroy(Review $review)
    {
        // Check if user owns this review or is admin
        if (Auth::id() !== $review->user_id && !Auth::user()->isAdmin()) {
            return redirect()->back()->with('error', 'Anda tidak memiliki akses untuk menghapus review ini.');
        }

        $review->delete();

        return redirect()->back()->with('success', 'Review berhasil dihapus.');
    }

    /**
     * Show form to edit review.
     */
    public function edit(Review $review)
    {
        // Check if user owns this review
        if (Auth::id() !== $review->user_id) {
            abort(403, 'Unauthorized access.');
        }

        return view('courses.reviews.edit', compact('review'));
    }
}
