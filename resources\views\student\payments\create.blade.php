@extends('layouts.app')

@section('title', 'Pembayaran Kursus - Sistem Kursus')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-2">
                    <i class="fas fa-credit-card me-2 text-primary"></i>Pembayaran Kursus
                </h1>
                <p class="text-muted">Lengkapi pembayaran untuk mengakses kursus</p>
            </div>

            <!-- Course Summary -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Ringkasan Kursus
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-2">{{ $course->title }}</h5>
                            <p class="text-muted mb-2">
                                <i class="fas fa-user me-1"></i>
                                Pengajar: {{ $course->instructor->name ?? 'Belum ditentukan' }}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fas fa-users me-1"></i>
                                {{ $course->total_students }} siswa terdaftar
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h3 class="text-primary mb-0">{{ $course->formatted_price }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-upload me-2"></i>Upload Bukti Pembayaran
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('student.payments.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="course_id" value="{{ $course->id }}">
                        
                        <!-- Amount -->
                        <div class="mb-4">
                            <label for="amount" class="form-label">
                                <i class="fas fa-money-bill me-2"></i>Jumlah Pembayaran
                                <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" 
                                       class="form-control @error('amount') is-invalid @enderror" 
                                       id="amount" 
                                       name="amount" 
                                       value="{{ old('amount', $course->price) }}" 
                                       min="0"
                                       step="1000"
                                       required>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Pastikan jumlah sesuai dengan harga kursus
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="mb-4">
                            <label for="payment_method" class="form-label">
                                <i class="fas fa-credit-card me-2"></i>Metode Pembayaran
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                    id="payment_method" 
                                    name="payment_method" 
                                    required>
                                <option value="">Pilih Metode Pembayaran</option>
                                <option value="Bank Transfer" {{ old('payment_method') == 'Bank Transfer' ? 'selected' : '' }}>
                                    Bank Transfer
                                </option>
                                <option value="E-Wallet" {{ old('payment_method') == 'E-Wallet' ? 'selected' : '' }}>
                                    E-Wallet (OVO, GoPay, DANA)
                                </option>
                                <option value="Virtual Account" {{ old('payment_method') == 'Virtual Account' ? 'selected' : '' }}>
                                    Virtual Account
                                </option>
                                <option value="Credit Card" {{ old('payment_method') == 'Credit Card' ? 'selected' : '' }}>
                                    Credit Card
                                </option>
                            </select>
                            @error('payment_method')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Payment Instructions -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Informasi Pembayaran
                            </h6>
                            <hr>
                            <p class="mb-2"><strong>Bank Transfer:</strong></p>
                            <ul class="mb-2">
                                <li>BCA: ********** a.n. Sistem Kursus</li>
                                <li>Mandiri: ********** a.n. Sistem Kursus</li>
                                <li>BNI: ********** a.n. Sistem Kursus</li>
                            </ul>
                            <p class="mb-2"><strong>E-Wallet:</strong></p>
                            <ul class="mb-0">
                                <li>OVO/GoPay/DANA: ************</li>
                            </ul>
                        </div>

                        <!-- Proof File Upload -->
                        <div class="mb-4">
                            <label for="proof_file" class="form-label">
                                <i class="fas fa-file-upload me-2"></i>Bukti Pembayaran
                                <span class="text-danger">*</span>
                            </label>
                            <input type="file" 
                                   class="form-control @error('proof_file') is-invalid @enderror" 
                                   id="proof_file" 
                                   name="proof_file" 
                                   accept=".jpg,.jpeg,.png,.pdf"
                                   required>
                            @error('proof_file')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="fas fa-file-image me-1"></i>
                                Format yang didukung: JPG, PNG, PDF (Maksimal 2MB)
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div id="filePreview" class="mb-4" style="display: none;">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div id="previewContent"></div>
                                    <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="removeFile()">
                                        <i class="fas fa-trash me-1"></i>Hapus File
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    Saya menyetujui <a href="#" class="text-primary">syarat dan ketentuan</a> pembayaran
                                    <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Kirim Bukti Pembayaran
                            </button>
                            <a href="{{ route('courses.show', $course) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Kembali ke Kursus
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Payment Process Info -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-clock me-2"></i>Proses Verifikasi
                    </h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="mb-2">
                                <i class="fas fa-upload fa-2x text-primary"></i>
                            </div>
                            <small class="text-muted">1. Upload Bukti</small>
                        </div>
                        <div class="col-4">
                            <div class="mb-2">
                                <i class="fas fa-search fa-2x text-warning"></i>
                            </div>
                            <small class="text-muted">2. Verifikasi Admin</small>
                        </div>
                        <div class="col-4">
                            <div class="mb-2">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                            <small class="text-muted">3. Akses Kursus</small>
                        </div>
                    </div>
                    <hr>
                    <p class="text-muted text-center mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        Verifikasi pembayaran biasanya memakan waktu 1-24 jam kerja
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('proof_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('filePreview');
    const previewContent = document.getElementById('previewContent');
    
    if (file) {
        preview.style.display = 'block';
        
        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.className = 'img-fluid rounded';
            img.style.maxHeight = '200px';
            previewContent.innerHTML = '';
            previewContent.appendChild(img);
        } else if (file.type === 'application/pdf') {
            previewContent.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-file-pdf fa-4x text-danger mb-2"></i>
                    <p class="mb-0">${file.name}</p>
                    <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                </div>
            `;
        }
    } else {
        preview.style.display = 'none';
    }
});

function removeFile() {
    document.getElementById('proof_file').value = '';
    document.getElementById('filePreview').style.display = 'none';
}

// Format number input
document.getElementById('amount').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value) {
        e.target.value = value;
    }
});
</script>
@endsection
