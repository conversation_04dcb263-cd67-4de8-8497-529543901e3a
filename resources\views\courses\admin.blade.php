@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Sistem Kursus')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
            <i class="fas fa-cogs me-2"></i><PERSON><PERSON><PERSON>
        </h1>
        <p class="text-muted">Manajemen semua kursus yang tersedia</p>
    </div>
    <a href="{{ route('courses.create') }}" class="btn btn-success btn-lg">
        <i class="fas fa-plus me-2"></i>Tambah Kursus
    </a>
</div>

@if($courses->isEmpty())
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-folder-open text-muted" style="font-size: 4rem;"></i>
    </div>
    <h3 class="text-muted">Belum Ada Kursus</h3>
    <p class="text-muted mb-4"><PERSON><PERSON> dengan menambahkan kursus pertama Anda.</p>
    <a href="{{ route('courses.create') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>Tambah Kursus Pertama
    </a>
</div>
@else
<!-- Responsive Table -->
<div class="card shadow-sm border-0">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>Daftar Kursus
        </h5>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="border-0 fw-semibold text-dark">#</th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-book me-1"></i>Judul
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-user-tie me-1"></i>Pengajar
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-tag me-1"></i>Harga
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-calendar me-1"></i>Tanggal Dibuat
                        </th>
                        <th class="border-0 fw-semibold text-dark text-center">
                            <i class="fas fa-cog me-1"></i>Aksi
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($courses as $index => $course)
                    <tr>
                        <td class="align-middle">
                            <span class="badge bg-primary">{{ $courses->firstItem() + $index }}</span>
                        </td>
                        <td class="align-middle">
                            <div class="fw-semibold text-dark">{{ $course->title }}</div>
                            <small class="text-muted">{{ Str::limit($course->description, 50) }}</small>
                        </td>
                        <td class="align-middle">
                            <span class="badge bg-info">{{ $course->instructor_name }}</span>
                        </td>
                        <td class="align-middle">
                            <span class="fw-bold text-success">{{ $course->formatted_price }}</span>
                        </td>
                        <td class="align-middle">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ $course->created_at->format('d/m/Y H:i') }}
                            </small>
                        </td>
                        <td class="align-middle text-center">
                            <div class="btn-group" role="group">
                                <!-- Edit Button -->
                                <a href="{{ route('courses.edit', $course) }}" 
                                   class="btn btn-outline-warning btn-sm" 
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <!-- Delete Button -->
                                <form action="{{ route('courses.destroy', $course) }}" 
                                      method="POST" 
                                      class="d-inline"
                                      onsubmit="return confirm('Apakah Anda yakin ingin menghapus kursus ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="btn btn-outline-danger btn-sm" 
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    
    @if($courses->hasPages())
    <div class="card-footer bg-light border-0">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted small">
                Menampilkan {{ $courses->firstItem() }} sampai {{ $courses->lastItem() }} dari {{ $courses->total() }} hasil
            </div>
            {{ $courses->links() }}
        </div>
    </div>
    @endif
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">{{ $courses->total() }}</h4>
                        <p class="mb-0">Total Kursus</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">{{ $courses->where('price', '>', 0)->count() }}</h4>
                        <p class="mb-0">Kursus Berbayar</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">{{ $courses->where('price', 0)->count() }}</h4>
                        <p class="mb-0">Kursus Gratis</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gift fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">Rp {{ number_format($courses->avg('price'), 0, ',', '.') }}</h4>
                        <p class="mb-0">Harga Rata-rata</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection