<?php

namespace Database\Seeders;

use App\Models\Course;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = [
            [
                'title' => 'Laravel untuk Pemula',
                'description' => 'Pelajari framework Laravel dari dasar hingga mahir. Kursus ini mencakup routing, controller, model, view, database migration, dan fitur-fitur canggih Laravel lainnya.',
                'instructor_name' => 'Budi Santoso',
                'price' => 299000,
            ],
            [
                'title' => 'React.js Fundamental',
                'description' => 'Kuasai React.js untuk membangun aplikasi web modern. Mulai dari component, state management, hooks, hingga integrasi dengan API.',
                'instructor_name' => '<PERSON>',
                'price' => 450000,
            ],
            [
                'title' => 'HTML & CSS Dasar',
                'description' => 'Pelajari dasar-dasar web development dengan HTML dan CSS. Cocok untuk pemula yang ingin memulai karir sebagai web developer.',
                'instructor_name' => '<PERSON>',
                'price' => 0,
            ],
            [
                'title' => 'Database MySQL',
                'description' => 'Pelajari manajemen database MySQL dari basic query hingga optimasi performa. Termasuk stored procedure, trigger, dan indexing.',
                'instructor_name' => 'Dr. Maria Santos',
                'price' => 350000,
            ],
            [
                'title' => 'JavaScript ES6+',
                'description' => 'Menguasai JavaScript modern dengan ES6+ features. Arrow functions, destructuring, async/await, modules, dan banyak lagi.',
                'instructor_name' => 'John Developer',
                'price' => 275000,
            ],
            [
                'title' => 'Python untuk Data Science',
                'description' => 'Belajar Python untuk analisis data dan machine learning. Menggunakan library pandas, numpy, matplotlib, dan scikit-learn.',
                'instructor_name' => 'Prof. Lisa Chen',
                'price' => 525000,
            ],
            [
                'title' => 'Git & GitHub',
                'description' => 'Pelajari version control dengan Git dan kolaborasi menggunakan GitHub. Essential skill untuk setiap programmer.',
                'instructor_name' => 'DevOps Master',
                'price' => 0,
            ],
            [
                'title' => 'Vue.js Complete Guide',
                'description' => 'Framework JavaScript yang mudah dipelajari untuk membangun user interface yang reaktif dan modern.',
                'instructor_name' => 'Frontend Guru',
                'price' => 399000,
            ],
            [
                'title' => 'Node.js & Express',
                'description' => 'Bangun REST API dan web application menggunakan Node.js dan Express.js. Termasuk authentication dan database integration.',
                'instructor_name' => 'Backend Expert',
                'price' => 425000,
            ],
            [
                'title' => 'UI/UX Design Principles',
                'description' => 'Pelajari prinsip-prinsip design yang baik untuk menciptakan user experience yang optimal. Dari wireframing hingga prototyping.',
                'instructor_name' => 'Design Master',
                'price' => 375000,
            ],
        ];

        foreach ($courses as $course) {
            Course::create($course);
        }
    }
}