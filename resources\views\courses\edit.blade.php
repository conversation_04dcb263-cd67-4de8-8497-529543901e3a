@extends('layouts.app')

@section('title', 'Edit Kursus - Sistem Kursus')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-warning text-dark">
                <h4 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Kursus: {{ $course->title }}
                </h4>
            </div>
            
            <div class="card-body p-4">
                <form action="{{ route('courses.update', $course) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <!-- Title Field -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            <i class="fas fa-book me-1 text-primary"></i>Judul Kursus
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $course->title) }}" 
                               placeholder="Masukkan judul kursus"
                               required>
                        @error('title')
                        <div class="invalid-feedback">
                            <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                        </div>
                        @enderror
                    </div>
                    
                    <!-- Instructor Name Field -->
                    <div class="mb-4">
                        <label for="instructor_name" class="form-label fw-semibold">
                            <i class="fas fa-user-tie me-1 text-primary"></i>Nama Pengajar
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg @error('instructor_name') is-invalid @enderror" 
                               id="instructor_name" 
                               name="instructor_name" 
                               value="{{ old('instructor_name', $course->instructor_name) }}" 
                               placeholder="Masukkan nama pengajar"
                               required>
                        @error('instructor_name')
                        <div class="invalid-feedback">
                            <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                        </div>
                        @enderror
                    </div>
                    
                    <!-- Description Field -->
                    <div class="mb-4">
                        <label for="description" class="form-label fw-semibold">
                            <i class="fas fa-align-left me-1 text-primary"></i>Deskripsi
                        </label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="4" 
                                  placeholder="Masukkan deskripsi kursus"
                                  required>{{ old('description', $course->description) }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">
                            <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                        </div>
                        @enderror
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Jelaskan secara detail tentang kursus yang akan diajarkan
                        </div>
                    </div>
                    
                    <!-- Price Field -->
                    <div class="mb-4">
                        <label for="price" class="form-label fw-semibold">
                            <i class="fas fa-tag me-1 text-primary"></i>Harga (Rp)
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light">Rp</span>
                            <input type="number" 
                                   class="form-control @error('price') is-invalid @enderror" 
                                   id="price" 
                                   name="price" 
                                   value="{{ old('price', $course->price) }}" 
                                   placeholder="0"
                                   min="0"
                                   step="1000"
                                   required>
                            @error('price')
                            <div class="invalid-feedback">
                                <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                            </div>
                            @enderror
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Masukkan 0 untuk kursus gratis
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ route('courses.admin') }}" class="btn btn-outline-secondary btn-lg me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Kembali
                        </a>
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="fas fa-save me-2"></i>Update Kursus
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Course Info Card -->
        <div class="card mt-4 border-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informasi Kursus
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>Dibuat:</strong><br>
                            <small class="text-muted">{{ $course->created_at->format('d F Y, H:i') }}</small>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>Terakhir Diupdate:</strong><br>
                            <small class="text-muted">{{ $course->updated_at->format('d F Y, H:i') }}</small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection