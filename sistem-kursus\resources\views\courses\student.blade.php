@extends('layouts.app')

@section('title', 'Daftar Kursus - Sistem Kursus')

@section('content')
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-5 fw-bold text-center text-primary mb-2">
            <i class="fas fa-book-open me-3"></i>Daftar Kursus
        </h1>
        <p class="text-center text-muted lead">Temukan kursus terbaik untuk mengembangkan kemampuan Anda</p>
    </div>
</div>

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

@if($courses->isEmpty())
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-book-open text-muted" style="font-size: 4rem;"></i>
    </div>
    <h3 class="text-muted">Belum Ada Kursus</h3>
    <p class="text-muted">Kursus akan segera tersedia. Silakan kembali lagi nanti.</p>
</div>
@else
<div class="row g-4">
    @foreach($courses as $course)
    <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0 card-hover">
            <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h5 class="card-title mb-0 text-truncate">
                    <i class="fas fa-graduation-cap me-2"></i>{{ $course->title }}
                </h5>
            </div>
            
            <div class="card-body d-flex flex-column">
                <div class="mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-user-tie me-1"></i>Pengajar
                    </h6>
                    <p class="fw-semibold text-dark">{{ $course->instructor_name }}</p>
                </div>
                
                <div class="mb-3 flex-grow-1">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-info-circle me-1"></i>Deskripsi
                    </h6>
                    <p class="text-dark">{{ Str::limit($course->description, 100) }}</p>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-muted mb-1">
                        <i class="fas fa-tag me-1"></i>Harga
                    </h6>
                    <h4 class="text-primary fw-bold">{{ $course->formatted_price }}</h4> 
                    
                </div>
             

                <!-- Status Pendaftaran -->
                @auth
                    @php
                        $isRegistered = $course->students()->where('user_id', auth()->id())->exists();
                    @endphp
                    
                    @if($isRegistered)
                    <div class="mb-3">
                        <div class="alert alert-success py-2 mb-0">
                            <i class="fas fa-check-circle me-2"></i>Anda sudah terdaftar
                        </div>
                    </div>
                    @endif
                @endauth
            </div>
            
            <div class="card-footer bg-transparent border-0 pt-0">
                @guest
                    <a href="{{ route('login') }}" class="btn btn-outline-primary btn-lg w-100 fw-semibold">
                        <i class="fas fa-sign-in-alt me-2"></i>Login untuk Mendaftar
                    </a>
                @else
                    @php
                        $isRegistered = $course->students()->where('user_id', auth()->id())->exists();
                    @endphp
                    
                    @if($isRegistered)
                        <a href="{{ route('courses.show', $course) }}" class="btn btn-success btn-lg w-100 fw-semibold">
                            <i class="fas fa-eye me-2"></i>Lihat Kursus
                        </a>
                    @else
                        <form action="{{ route('courses.register', $course) }}" method="POST" class="d-inline w-100">
                            @csrf
                            <button type="submit" class="btn btn-primary btn-lg w-100 fw-semibold" 
                                    onclick="return confirm('Apakah Anda yakin ingin mendaftar kursus ini?')">
                                <i class="fas fa-user-plus me-2"></i>Daftar Sekarang
                            </button>
                        </form>
                    @endif
                @endguest
            </div>
        </div>
    </div>
    @endforeach
</div>

<!-- Pagination -->
@if($courses instanceof \Illuminate\Pagination\LengthAwarePaginator)
<div class="d-flex justify-content-center mt-4">
    {{ $courses->links() }}
</div>
@endif

<!-- Statistics -->
<div class="mt-5 py-4 bg-primary rounded text-white text-center">
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-book text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold">{{ $courses->count() }}</h3>
            <p class="mb-0">Kursus Tersedia</p>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-users text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold">{{ \App\Models\User::whereHas('studentCourses')->count() }}+</h3>
            <p class="mb-0">Siswa Terdaftar</p>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-star text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold">4.9</h3>
            <p class="mb-0">Rating Rata-rata</p>
        </div>
    </div>
</div>
@endif
@endsection