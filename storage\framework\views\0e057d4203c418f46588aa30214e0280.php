

<?php $__env->startSection('title', 'Daftar Kursus - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-book me-2 text-primary"></i>Daftar Kursus
                    </h1>
                    <p class="text-muted mb-0">Temukan kursus yang tepat untuk mengembangkan skill Anda</p>
                </div>
                <div>
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="Cari kursus..." id="searchCourse">
                        <button class="btn btn-outline-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row">
        <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title"><?php echo e($course->title); ?></h5>
                            <span class="badge bg-primary"><?php echo e($course->formatted_price); ?></span>
                        </div>

                        <p class="card-text text-muted">
                            <?php echo e(Str::limit($course->description, 100)); ?>

                        </p>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                Pengajar: <?php echo e($course->instructor->name ?? 'Belum ditentukan'); ?>

                            </small>
                        </div>

                        <?php if($course->reviews_count > 0): ?>
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="text-warning me-2">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo e($i <= $course->average_rating ? '' : '-o'); ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <small class="text-muted">
                                        (<?php echo e($course->reviews_count); ?> review<?php echo e($course->reviews_count > 1 ? 's' : ''); ?>)
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                <?php echo e($course->total_students); ?> siswa terdaftar
                            </small>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('courses.show', $course)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                            <?php if(auth()->guard()->check()): ?>
                                <?php if(!auth()->user()->enrolledCourses->contains($course->id)): ?>
                                    <form action="<?php echo e(route('student.courses.enroll', $course)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-user-plus me-1"></i>Daftar Kursus
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <button class="btn btn-success w-100" disabled>
                                        <i class="fas fa-check me-1"></i>Sudah Terdaftar
                                    </button>
                                <?php endif; ?>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login untuk Daftar
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Belum Ada Kursus</h4>
                    <p class="text-muted">Kursus akan segera tersedia. Silakan cek kembali nanti.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php if($courses->count() > 0): ?>
        <!-- Pagination would go here if using paginate() -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <p class="text-muted">Menampilkan <?php echo e($courses->count()); ?> kursus</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.getElementById('searchCourse').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const courseCards = document.querySelectorAll('.col-md-6');

    courseCards.forEach(card => {
        const title = card.querySelector('.card-title').textContent.toLowerCase();
        const description = card.querySelector('.card-text').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/courses/index.blade.php ENDPATH**/ ?>