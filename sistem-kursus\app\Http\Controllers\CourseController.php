<?php

namespace App\Http\Controllers;

use App\Models\Course;
use Illuminate\Http\Request;

class Course<PERSON><PERSON>roller extends Controller
{
    /**
     * Display a listing of the resource (Student View).
     */
    public function index()
    {
        $courses = Course::orderBy('created_at', 'desc')->get();
        return view('courses.index', compact('courses'));
    }

    /**
     * Display a listing of the resource (Admin/Teacher View).
     */
    public function admin()
    {
        $courses = Course::orderBy('created_at', 'desc')->paginate(10);
        return view('courses.admin', compact('courses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('courses.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor_name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0'
        ]);

        Course::create($request->all());

        return redirect()->route('courses.admin')
            ->with('success', 'Kursus berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        return view('courses.show', compact('course'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Course $course)
    {
        return view('courses.edit', compact('course'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Course $course)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor_name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0'
        ]);

        $course->update($request->all());

        return redirect()->route('courses.admin')
            ->with('success', 'Kursus berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Course $course)
    {
        $course->delete();

        return redirect()->route('courses.admin')
            ->with('success', 'Kursus berhasil dihapus!');
    }

    /**
     * Register student to course.
     */
    public function register(Course $course)
    {
        // Implement registration logic here
        return redirect()->back()
            ->with('success', 'Berhasil mendaftar kursus: ' . $course->title);
    }
}