<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Payment;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PaymentController extends Controller
{
    /**
     * Display student's payments.
     */
    public function index()
    {
        $payments = Auth::user()->payments()
            ->with(['course'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('student.payments.index', compact('payments'));
    }

    /**
     * Show payment form for a course.
     */
    public function create(Course $course)
    {
        // Check if user is enrolled in the course
        if (!Auth::user()->enrollments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda harus mendaftar kursus terlebih dahulu.');
        }

        // Check if payment already exists
        if (Auth::user()->payments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda sudah melakukan pembayaran untuk kursus ini.');
        }

        return view('student.payments.create', compact('course'));
    }

    /**
     * Store payment with proof file.
     */
    public function store(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|string|max:255',
            'proof_file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:2048',
        ]);

        $course = Course::findOrFail($request->course_id);

        // Check if user is enrolled
        if (!Auth::user()->enrollments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda harus mendaftar kursus terlebih dahulu.');
        }

        // Check if payment already exists
        if (Auth::user()->payments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda sudah melakukan pembayaran untuk kursus ini.');
        }

        // Store proof file
        $proofFile = $request->file('proof_file');
        $fileName = time() . '_' . $proofFile->getClientOriginalName();
        $filePath = $proofFile->storeAs('payment_proofs', $fileName, 'public');

        // Create payment record
        Payment::create([
            'user_id' => Auth::id(),
            'course_id' => $course->id,
            'amount' => $request->amount,
            'payment_method' => $request->payment_method,
            'proof_file' => $filePath,
            'status' => 'pending',
        ]);

        return redirect()->route('student.payments.index')
            ->with('success', 'Bukti pembayaran berhasil diunggah. Menunggu verifikasi admin.');
    }

    /**
     * Admin view - Display all payments.
     */
    public function adminIndex(Request $request)
    {
        $query = Payment::with(['user', 'course', 'verifiedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by user name or course title
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('course', function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%");
            });
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.payments.index', compact('payments'));
    }

    /**
     * Verify payment (admin only).
     */
    public function verify(Request $request, Payment $payment)
    {
        $request->validate([
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:500',
        ]);

        $payment->update([
            'status' => $request->status,
            'notes' => $request->notes,
            'verified_at' => now(),
            'verified_by' => Auth::id(),
        ]);

        $statusText = $request->status === 'verified' ? 'diverifikasi' : 'ditolak';

        return redirect()->back()->with('success', "Pembayaran berhasil {$statusText}.");
    }

    /**
     * Download payment proof file.
     */
    public function downloadProof(Payment $payment)
    {
        // Check if user can access this payment
        if (!Auth::user()->isAdmin() && Auth::id() !== $payment->user_id) {
            abort(403, 'Unauthorized access.');
        }

        if (!Storage::disk('public')->exists($payment->proof_file)) {
            return redirect()->back()->with('error', 'File bukti pembayaran tidak ditemukan.');
        }

        return Storage::disk('public')->download($payment->proof_file);
    }
}
