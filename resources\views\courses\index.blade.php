@extends('layouts.app')

@section('title', 'Daftar Kursus - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-book me-2 text-primary"></i>Daftar Kursus
                    </h1>
                    <p class="text-muted mb-0">Temukan kursus yang tepat untuk mengembangkan skill Anda</p>
                </div>
                <div>
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="Cari kursus..." id="searchCourse">
                        <button class="btn btn-outline-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row">
        @forelse ($courses as $course)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title">{{ $course->title }}</h5>
                            <span class="badge bg-primary">{{ $course->formatted_price }}</span>
                        </div>

                        <p class="card-text text-muted">
                            {{ Str::limit($course->description, 100) }}
                        </p>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                Pengajar: {{ $course->instructor->name ?? 'Belum ditentukan' }}
                            </small>
                        </div>

                        @if($course->reviews_count > 0)
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="text-warning me-2">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= $course->average_rating ? '' : '-o' }}"></i>
                                        @endfor
                                    </div>
                                    <small class="text-muted">
                                        ({{ $course->reviews_count }} review{{ $course->reviews_count > 1 ? 's' : '' }})
                                    </small>
                                </div>
                            </div>
                        @endif

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                {{ $course->total_students }} siswa terdaftar
                            </small>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <a href="{{ route('courses.show', $course) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                            @auth
                                @if(!auth()->user()->enrolledCourses->contains($course->id))
                                    <form action="{{ route('student.courses.enroll', $course) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-user-plus me-1"></i>Daftar Kursus
                                        </button>
                                    </form>
                                @else
                                    <button class="btn btn-success w-100" disabled>
                                        <i class="fas fa-check me-1"></i>Sudah Terdaftar
                                    </button>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login untuk Daftar
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Belum Ada Kursus</h4>
                    <p class="text-muted">Kursus akan segera tersedia. Silakan cek kembali nanti.</p>
                </div>
            </div>
        @endforelse
    </div>

    @if($courses->count() > 0)
        <!-- Pagination would go here if using paginate() -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <p class="text-muted">Menampilkan {{ $courses->count() }} kursus</p>
            </div>
        </div>
    @endif
</div>

<script>
document.getElementById('searchCourse').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const courseCards = document.querySelectorAll('.col-md-6');

    courseCards.forEach(card => {
        const title = card.querySelector('.card-title').textContent.toLowerCase();
        const description = card.querySelector('.card-text').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});
</script>
@endsection
