@extends('layouts.app')

@section('title', 'Dashboard Pengajar - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-2">
                                <i class="fas fa-chalkboard-teacher me-2"></i>
                                Dashboard Pengajar
                            </h2>
                            <p class="card-text mb-0">
                                Selamat datang, {{ auth()->user()->name }}! Ke<PERSON>la kursus dan siswa Anda dengan mudah.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-chalkboard fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-book fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">Kursus Saya</h5>
                    <h3 class="text-primary">{{ auth()->user()->taughtCourses()->count() }}</h3>
                    <small class="text-muted">Kursus yang diajar</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Total Siswa</h5>
                    <h3 class="text-success">
                        {{ auth()->user()->taughtCourses()->withCount('enrollments')->get()->sum('enrollments_count') }}
                    </h3>
                    <small class="text-muted">Siswa terdaftar</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">Rating Rata-rata</h5>
                    <h3 class="text-warning">
                        {{ number_format(auth()->user()->taughtCourses()->withAvg('reviews', 'rating')->get()->avg('reviews_avg_rating') ?? 0, 1) }}
                    </h3>
                    <small class="text-muted">Dari 5 bintang</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center card-hover bg-light">
                <div class="card-body">
                    <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                    <h5 class="card-title">Total Materi</h5>
                    <h3 class="text-info">
                        {{ auth()->user()->taughtCourses()->withCount('materials')->get()->sum('materials_count') }}
                    </h3>
                    <small class="text-muted">Materi pembelajaran</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- My Courses -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Kursus yang Saya Ajar
                    </h5>
                    <a href="{{ route('teacher.courses.index') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-list me-1"></i>Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @forelse(auth()->user()->taughtCourses()->withCount(['enrollments', 'reviews'])->latest()->take(5)->get() as $course)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $course->title }}</h6>
                                <small class="text-muted">
                                    {{ $course->enrollments_count }} siswa • 
                                    {{ $course->reviews_count }} review • 
                                    {{ $course->formatted_price }}
                                </small>
                                <br>
                                <small class="text-muted">
                                    Dibuat: {{ $course->created_at->format('d M Y') }}
                                </small>
                            </div>
                            <div>
                                @if($course->reviews_count > 0)
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star"></i> {{ number_format($course->average_rating, 1) }}
                                    </span>
                                @endif
                                <a href="{{ route('courses.show', $course) }}" class="btn btn-outline-primary btn-sm ms-2">
                                    <i class="fas fa-eye me-1"></i>Lihat
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Anda belum mengajar kursus apapun.</p>
                            <small class="text-muted">Hubungi admin untuk mendapatkan kursus yang dapat Anda ajar.</small>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Activity -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Aksi Cepat
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('teacher.courses.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-book me-2"></i>Kelola Kursus
                        </a>
                        <a href="#" class="btn btn-outline-success">
                            <i class="fas fa-file-alt me-2"></i>Kelola Materi
                        </a>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fas fa-users me-2"></i>Lihat Siswa
                        </a>
                        <a href="#" class="btn btn-outline-warning">
                            <i class="fas fa-star me-2"></i>Review & Rating
                        </a>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-user-cog me-2"></i>Pengaturan Profil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Reviews -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-star me-2"></i>Review Terbaru
                    </h6>
                </div>
                <div class="card-body">
                    @php
                        $recentReviews = \App\Models\Review::whereHas('course', function($q) {
                            $q->where('instructor_id', auth()->id());
                        })->with(['user', 'course'])->latest()->take(3)->get();
                    @endphp
                    
                    @forelse($recentReviews as $review)
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="d-flex align-items-center mb-1">
                                <strong class="me-2">{{ $review->user->name }}</strong>
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= $review->rating ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                            </div>
                            <small class="text-muted">{{ $review->course->title }}</small>
                            @if($review->review)
                                <p class="small mb-1">{{ Str::limit($review->review, 80) }}</p>
                            @endif
                            <small class="text-muted">{{ $review->created_at->diffForHumans() }}</small>
                        </div>
                    @empty
                        <small class="text-muted">Belum ada review</small>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
