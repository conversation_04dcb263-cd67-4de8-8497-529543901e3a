<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Drop the old instructor_name column
            $table->dropColumn('instructor_name');

            // Add instructor_id foreign key
            $table->foreignId('instructor_id')->constrained('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Drop foreign key and instructor_id column
            $table->dropForeign(['instructor_id']);
            $table->dropColumn('instructor_id');

            // Add back instructor_name column
            $table->string('instructor_name');
        });
    }
};
