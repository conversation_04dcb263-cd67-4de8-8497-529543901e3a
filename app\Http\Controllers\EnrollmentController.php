<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Enrollment;
use App\Models\Course;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class EnrollmentController extends Controller
{
    /**
     * Display student's enrollments.
     */
    public function index()
    {
        $enrollments = Auth::user()->enrollments()
            ->with(['course.instructor'])
            ->orderBy('enrolled_at', 'desc')
            ->paginate(10);

        return view('student.enrollments.index', compact('enrollments'));
    }

    /**
     * Store a new enrollment (student enrolls in course).
     */
    public function store(Request $request, Course $course)
    {
        $user = Auth::user();

        // Check if already enrolled
        if ($user->enrollments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('error', 'Anda sudah terdaftar di kursus ini.');
        }

        // Create enrollment
        Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'active',
        ]);

        return redirect()->back()->with('success', 'Berhasil mendaftar kursus: ' . $course->title);
    }

    /**
     * Admin view - Display all enrollments.
     */
    public function adminIndex(Request $request)
    {
        $query = Enrollment::with(['user', 'course']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by user name or course title
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('course', function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%");
            });
        }

        $enrollments = $query->orderBy('enrolled_at', 'desc')->paginate(15);

        return view('admin.enrollments.index', compact('enrollments'));
    }

    /**
     * Update enrollment status (admin only).
     */
    public function updateStatus(Request $request, Enrollment $enrollment)
    {
        $request->validate([
            'status' => 'required|in:active,completed,cancelled',
        ]);

        $enrollment->update([
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'Status pendaftaran berhasil diperbarui.');
    }

    /**
     * Remove enrollment.
     */
    public function destroy(Enrollment $enrollment)
    {
        // Only allow student to cancel their own enrollment or admin
        if (Auth::user()->id !== $enrollment->user_id && !Auth::user()->isAdmin()) {
            return redirect()->back()->with('error', 'Anda tidak memiliki akses untuk membatalkan pendaftaran ini.');
        }

        $enrollment->delete();

        return redirect()->back()->with('success', 'Pendaftaran kursus berhasil dibatalkan.');
    }
}
